class EmailDecoder {
    // 解码 Base64 编码的 UTF-8 文本
    static decodeBase64UTF8(base64) {
        try {
            const decoded = atob(base64);
            return decodeURIComponent(escape(decoded));
        } catch (e) {
            console.error('Base64 解码失败:', e);
            return base64;
        }
    }

    // 解码 Quoted-Printable 编码
    static decodeQuotedPrintable(str) {
        return str.replace(/=\r\n/g, '')
            .replace(/=([0-9A-F]{2})/g, (_, p1) => 
                String.fromCharCode(parseInt(p1, 16)));
    }

    // 提取邮件头信息
    static extractHeaders(rawEmail) {
        const headers = {
            subject: '',
            from: '',
            to: '',
            date: ''
        };

        // 提取并解码主题
        const subjectMatch = rawEmail.match(/Subject:\s*([^\r\n]+)/);
        if (subjectMatch) {
            headers.subject = subjectMatch[1].replace(
                /=\?UTF-8\?B\?(.*?)\?=/g,
                (_, p1) => this.decodeBase64UTF8(p1)
            );
        }

        // 提取其他头信息
        headers.from = rawEmail.match(/From:\s*([^\r\n]+)/)?.[1] || '未知';
        headers.to = rawEmail.match(/To:\s*([^\r\n]+)/)?.[1] || '未知';
        headers.date = rawEmail.match(/Date:\s*([^\r\n]+)/)?.[1] || '未知';

        return headers;
    }

    // 优化验证码提取方法
    static extractVerificationCode(content) {
        // 定义需要排除的关键词（多语言支持）
        const excludeKeywords = [
            // 注册相关
            '注册', '登记', '등록', 'register', 'registration',
            // 编号相关
            '编号', '号码', '번호', 'number', 'No.',
            // 电话相关
            '电话', '手机', '联系', 'tel', 'phone', 'contact',
            // 地址相关
            'address', '地址', 'location',
            // 公司相关
            'Limited', 'Ltd', 'Inc', 'Corporation', 'Corp',
            // 其他可能的标识符
            'ID', 'ref', 'reference'
        ];

        // 优化验证码匹配模式，按优先级排序
        const verificationPatterns = [
            // 高优先级：明确的验证码标识
            /(?:验证码|verification code|code)[^0-9]*([0-9]{6})(?!\d)/i,
            /您的验证码(?:是|为)?[^0-9]*([0-9]{6})(?!\d)/,
            /[^0-9]([0-9]{6})[^0-9]*(?:是|为)(?:您的)?验证码/,
            
            // 常见格式
            /(?:动态验证码|一次性验证码|临时验证码)[^0-9]*([0-9]{6})(?!\d)/,
            /code is[^0-9]*([0-9]{6})(?!\d)/i,
            /verification code is[^0-9]*([0-9]{6})(?!\d)/i,
            
            // 多语言支持
            /驗證碼[^0-9]*([0-9]{6})(?!\d)/,  // 繁体中文
            /인증번호[^0-9]*([0-9]{6})(?!\d)/, // 韩文
            /認証コード[^0-9]*([0-9]{6})(?!\d)/, // 日文
            
            // 通用数字匹配（最低优先级）
            /\D([0-9]{6})(?!\d)/
        ];

        // 首先尝试使用验证码关键词匹配
        for (const pattern of verificationPatterns) {
            const match = content.match(pattern);
            if (match) {
                const code = match[1];
                // 验证码有效性检查
                if (code && /^\d{6}$/.test(code)) {
                    // 检查上下文是否包含排除关键词
                    const contextStart = Math.max(0, content.indexOf(code) - 50);
                    const contextEnd = Math.min(content.length, content.indexOf(code) + 50);
                    const context = content.substring(contextStart, contextEnd).toLowerCase();
                    
                    const hasExcludeKeyword = excludeKeywords.some(keyword => 
                        context.includes(keyword.toLowerCase())
                    );
                    
                    if (!hasExcludeKeyword) {
                        return code;
                    }
                }
            }
        }

        // 如果上述模式都没匹配到，尝试查找独立的6位数字
        const numberMatches = content.match(/\b\d{6}\b/g);
        if (numberMatches) {
            for (const num of numberMatches) {
                const contextStart = Math.max(0, content.indexOf(num) - 50);
                const contextEnd = Math.min(content.length, content.indexOf(num) + 50);
                const context = content.substring(contextStart, contextEnd).toLowerCase();
                
                // 检查上下文中是否包含验证码相关词语
                const hasVerificationKeyword = /验证码|code|verification|인증|認証/i.test(context);
                const hasExcludeKeyword = excludeKeywords.some(keyword => 
                    context.includes(keyword.toLowerCase())
                );
                
                if (hasVerificationKeyword && !hasExcludeKeyword) {
                    return num;
                }
            }
        }

        return null;
    }

    // 提取并解码邮件内容
    static extractContent(rawEmail) {
        let content = '';
        
        // HTML 内容提取
        const htmlMatch = rawEmail.match(/Content-Type: text\/html[\s\S]*?\r\n\r\n([\s\S]*?)(?:-{6}|$)/i);
        if (htmlMatch) {
            content = htmlMatch[1];
            
            if (rawEmail.includes('Content-Transfer-Encoding: base64')) {
                content = content.replace(/\s/g, '');
                try {
                    content = atob(content);
                    // 移除 HTML 标签，保留文本内容
                    content = content.replace(/<[^>]+>/g, '\n')
                                   .replace(/&nbsp;/g, ' ')
                                   .replace(/\s+/g, ' ')
                                   .trim();
                } catch (e) {
                    console.error('HTML Base64 解码失败:', e);
                }
            } else if (rawEmail.includes('Content-Transfer-Encoding: quoted-printable')) {
                content = this.decodeQuotedPrintable(content);
                content = content.replace(/<[^>]+>/g, '\n')
                               .replace(/&nbsp;/g, ' ')
                               .replace(/\s+/g, ' ')
                               .trim();
            }
        }

        // 如果没有 HTML 内容，尝试提取纯文本内容
        if (!content) {
            const textMatch = rawEmail.match(/Content-Type: text\/plain[\s\S]*?\r\n\r\n([\s\S]*?)(?:-{6}|$)/i);
            if (textMatch) {
                content = textMatch[1];
                
                if (rawEmail.includes('Content-Transfer-Encoding: base64')) {
                    content = content.replace(/\s/g, '');
                    try {
                        content = atob(content);
                    } catch (e) {
                        console.error('Text Base64 解码失败:', e);
                    }
                } else if (rawEmail.includes('Content-Transfer-Encoding: quoted-printable')) {
                    content = this.decodeQuotedPrintable(content);
                }
            }
        }

        // 提取验证码
        const verificationCode = this.extractVerificationCode(content);
        
        return {
            content: content || '无法解析邮件内容',
            verificationCode: verificationCode
        };
    }
} 