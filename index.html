<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adobe账号邮箱验证码查询</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 基础样式和页面UI */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        :root {
            --primary-color: #1e88e5;
            --primary-hover: #1565c0;
            --secondary-color: #f5f7fa;
            --text-color: #2c3e50;
            --light-text: #7f8c8d;
            --danger-color: #e74c3c;
            --success-color: #2ecc71;
            --border-radius: 12px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }

        body {
            background-color: var(--secondary-color);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-color);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            color: var(--text-color);
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: var(--light-text);
            font-size: 16px;
        }

        .header::after {
            content: '';
            display: block;
            width: 60px;
            height: 3px;
            background: var(--primary-color);
            margin: 20px auto 0;
            border-radius: 3px;
        }

        /* 警告卡片样式 */
        .warning-card {
            background: linear-gradient(135deg, #fff1f1, #fff5f5);
            border-radius: 12px;
            padding: 18px 20px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
            box-shadow: 0 3px 10px rgba(231, 76, 60, 0.15);
        }
        
        .warning-content.centered {
            text-align: center;
        }
        
        .warning-content h3 {
            font-size: 17px;
            font-weight: 600;
            color: #d63031;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
        }
        
        .warning-content h3 i {
            color: #e74c3c;
            font-size: 18px;
        }
        
        .warning-content p {
            font-size: 15px;
            color: #333;
            line-height: 1.5;
            margin: 0;
            font-weight: 500;
        }
        
        .warning-content .warning-extra {
            margin-top: 10px;
            font-size: 14px;
            color: #0984e3;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-weight: 500;
        }
        
        .warning-content .warning-extra i {
            color: #0984e3;
            font-size: 14px;
        }

        /* 重构的查询UI样式 - 更加明显的输入框 */
        .search-panel {
            display: flex;
            gap: 15px;
            margin: 30px 0;
        }
        
        .search-field {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .search-field:focus-within {
            box-shadow: 0 4px 12px rgba(30, 136, 229, 0.15);
            border-color: rgba(30, 136, 229, 0.3);
        }
        
        .search-icon {
            position: absolute;
            left: 16px;
            color: #1e88e5;
            font-size: 18px;
        }
        
        .search-input {
            width: 100%;
            padding: 18px 18px 18px 50px;
            border: none;
            font-size: 16px;
            color: #2c3e50;
            background: transparent;
        }
        
        .search-input:focus {
            outline: none;
        }
        
        .search-input::placeholder {
            color: #95a5a6;
        }
        
        .search-button {
            padding: 0 30px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(30, 136, 229, 0.2);
        }
        
        .search-button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(30, 136, 229, 0.3);
        }
        
        .search-button i {
            font-size: 16px;
        }
        
        @media screen and (max-width: 768px) {
            .search-panel {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-button {
                padding: 16px 0;
                justify-content: center;
            }
            
            .toast {
                top: 320px;  /* 移动设备上调整位置 */
            }
        }

        .toast {
            position: fixed;
            top: 240px;  /* 修改位置，放在输入框下方 */
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-size: 15px;
            opacity: 0;
            transition: opacity 0.3s, transform 0.3s;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 90%;
            text-align: center;
            line-height: 1.4;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);  /* 修改动画方向 */
        }

        /* 全新警告框样式 */
        .alert-box {
            background-color: #fff8f8;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            margin: 25px 0;
            border: 1px solid rgba(231, 76, 60, 0.2);
        }
        
        .alert-icon {
            background-color: #e74c3c;
            color: white;
            text-align: center;
            padding: 15px 0;
        }
        
        .alert-icon i {
            font-size: 24px;
        }
        
        .alert-content {
            padding: 20px;
            text-align: center;
        }
        
        .alert-content h3 {
            color: #e74c3c;
            font-size: 18px;
            margin-bottom: 12px;
            font-weight: 600;
        }
        
        .alert-content p {
            color: #333;
            font-size: 15px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .alert-footer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            color: #0984e3;
            font-size: 14px;
            font-weight: 500;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding-top: 12px;
        }
        
        .alert-footer i {
            margin-right: 5px;
        }
        
        .alert-footer .divider {
            color: #bbb;
        }

        /* 现代简约风格警告框样式 */
        .alert-modern {
            display: flex;
            background-color: #fff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
            margin: 25px 0;
            border: 1px solid rgba(0, 0, 0, 0.06);
            position: relative;
        }
        
        .alert-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: #e74c3c;
        }
        
        .alert-modern__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            flex-shrink: 0;
            background-color: rgba(231, 76, 60, 0.05);
        }
        
        .alert-modern__icon i {
            font-size: 28px;
            color: #e74c3c;
        }
        
        .alert-modern__content {
            flex: 1;
            padding: 20px;
        }
        
        .alert-modern__content h3 {
            color: #2d3436;
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .alert-modern__content p {
            color: #636e72;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 16px;
        }
        
        .alert-modern__footer {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .alert-tag {
            display: inline-flex;
            align-items: center;
            background-color: #f1f2f6;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            color: #2d3436;
            font-weight: 500;
        }
        
        .alert-tag i {
            color: #e74c3c;
            margin-right: 6px;
            font-size: 12px;
        }
        
        @media screen and (max-width: 768px) {
            .alert-modern {
                flex-direction: column;
            }
            
            .alert-modern__icon {
                width: 100%;
                height: 60px;
            }
            
            .alert-modern::before {
                width: 100%;
                height: 4px;
            }
        }

        /* 高级感警告框样式 - 文字居中 */
        .alert-premium {
            background-color: #fff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.04);
            margin: 25px 0;
            border: 1px solid rgba(0, 0, 0, 0.05);
            text-align: center;
            position: relative;
        }
        
        .alert-premium::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, #e74c3c, #f39c12);
        }
        
        .alert-premium__header {
            padding: 20px 20px 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .alert-premium__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: 50%;
            margin-bottom: 12px;
        }
        
        .alert-premium__icon i {
            font-size: 24px;
            color: #e74c3c;
        }
        
        .alert-premium__header h3 {
            color: #2d3436;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .alert-premium__content {
            padding: 0 30px 20px;
        }
        
        .alert-premium__content p {
            color: #636e72;
            font-size: 15px;
            line-height: 1.6;
            margin: 0;
        }
        
        .alert-premium__footer {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .alert-premium__tag {
            display: inline-flex;
            align-items: center;
            background-color: #fff;
            padding: 8px 16px;
            border-radius: 30px;
            font-size: 13px;
            color: #2d3436;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
        }
        
        .alert-premium__tag i {
            color: #e74c3c;
            margin-right: 6px;
            font-size: 12px;
        }
        
        @media screen and (max-width: 768px) {
            .alert-premium__content {
                padding: 0 20px 20px;
            }
            
            .alert-premium__footer {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
        }

        /* 简洁美观的警告框样式 */
        .alert-clean {
            background-color: #fff;
            border-radius: 14px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
            border-top: 3px solid #e74c3c;
        }
        
        .alert-clean h3 {
            color: #e74c3c;
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 12px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert-clean h3 i {
            font-size: 16px;
        }
        
        .alert-clean p {
            color: #4a4a4a;
            font-size: 15px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .alert-clean__tags {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 5px;
        }
        
        .alert-clean__tags span {
            color: #0984e3;
            font-size: 20px;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .alert-clean__tags span i {
            font-size: 18px;
        }
        
        @media screen and (max-width: 768px) {
            .alert-clean__tags {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>

    <!-- 单独拉出的结果UI样式 -->
    <style>
        /* 结果显示相关样式 */
        .email-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .timestamp-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .validity {
            display: flex;
            align-items: center;
            color: var(--text-color);
            font-size: 15px;
            gap: 6px;
        }

        .validity i {
            color: var(--primary-color);
        }

        .timestamp {
            color: var(--light-text);
            font-size: 15px;
            font-weight: 500;
        }

        .timestamp span {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            margin-left: 5px;
        }

        .verification-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }

        .verification-code {
            color: var(--text-color);
            font-size: 46px;
            font-weight: 700;
            letter-spacing: 5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.05);
            padding: 15px 0;
            text-align: center;
        }

        .copy-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
        }

        .copy-button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
        }

        .no-results {
            text-align: center;
            padding: 40px 20px;
            color: var(--light-text);
            background: white;
            border-radius: var(--border-radius);
            font-size: 16px;
        }

        .no-results i {
            font-size: 48px;
            color: #d0d0d0;
            margin-bottom: 15px;
            display: block;
        }

        .error {
            text-align: center;
            padding: 25px;
            color: var(--danger-color);
            background: rgba(231, 76, 60, 0.1);
            border-radius: var(--border-radius);
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        .error i {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .loading {
            text-align: center;
            padding: 40px 20px;
            color: var(--light-text);
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(30, 136, 229, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media screen and (max-width: 768px) {
            .verification-code {
                font-size: 36px;
                word-break: break-all;
                letter-spacing: 3px;
            }

            .verification-container {
                flex-direction: column;
                gap: 15px;
            }

            .copy-button {
                width: 100%;
                padding: 12px 0;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Adobe账号验证码查询</h1>
            <p>快速查询您的邮箱账号验证码</p>
        </div>
        
        <!-- 顶部警告提示 - 简洁居中设计 -->
        <div class="alert-clean">
            <h3><i class="fas fa-exclamation-circle"></i> 重要提示(必看)</h3>
            <p>若验证码输入错误，请勿重复输入，点击查询获取最新验证码</p>
            <div class="alert-clean__tags">
                <span><i class="fas fa-ban"></i> 禁止重复输入错误验证码</span>
                <span><i class="fas fa-ban"></i> 禁止多次点击发送验证码</span>
            </div>
            <p></p>
            <p>若因多次输入错误或多次点击发送验证码导致账号被锁定，请耐心等待10-15分钟后重新登录！</p>
        </div>

        <div class="search-panel">
            <div class="search-field">
                <i class="fas fa-envelope search-icon"></i>
                <input type="text" 
                       id="emailInput" 
                       class="search-input" 
                       placeholder="请输入购买的账号 例:<EMAIL>"
                       oninput="this.value=this.value.replace(/\s+/g,''); validateEmailInput()">
            </div>
            <button onclick="searchEmails()" class="search-button">
                <i class="fas fa-search"></i> 查询
            </button>
        </div>

        <div id="results"></div>
    </div>

    <script src="/static/js/emailDecoder.js"></script>
    <script>
        // 添加时间格式化函数
        function formatTime(dateString) {
            // 创建一个 UTC 时间对象
            const utcDate = new Date(dateString);
            
            // 转换为中国时区 (UTC+8)
            const chinaTime = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
            
            // 获取月份和日期
            const month = (chinaTime.getMonth() + 1).toString().padStart(2, '0');
            const day = chinaTime.getDate().toString().padStart(2, '0');
            // 获取时间（时分秒）
            const hours = chinaTime.getHours().toString().padStart(2, '0');
            const minutes = chinaTime.getMinutes().toString().padStart(2, '0');
            const seconds = chinaTime.getSeconds().toString().padStart(2, '0');
            
            return `${month}/${day} ${hours}:${minutes}:${seconds}`;
        }

        // 添加防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 预加载结果容器
        const resultsDiv = document.getElementById('results');
        
        // 验证邮箱格式是否符合要求
        function validateEmail(email) {
            return email.toLowerCase().endsWith('@552500.xyz');
        }
        
        // 实时验证输入框内容
        const validateEmailInput = debounce(function() {
            const emailInput = document.getElementById('emailInput');
            const email = emailInput.value.trim();
            
            // 只有当输入了@符号且不是以@552500.xyz结尾时才显示提示
            if (email.includes('@') && !validateEmail(email)) {
                showToast('<i class="fas fa-exclamation-triangle" style="color: var(--danger-color)"></i> 请输入正确购买的账号哦！输入的是你购买登录的账号，不是网址请不要搞混了哦~');
            }
        }, 1000); // 1秒的防抖延迟
        
        // 优化后的搜索函数
        async function searchEmails() {
            const emailInput = document.getElementById('emailInput');
            const email = emailInput.value.trim();

            if (!email) {
                resultsDiv.innerHTML = '<div class="error"><i class="fas fa-exclamation-circle"></i><p>请输入有效的邮箱地址</p></div>';
                return;
            }
            
            // 验证邮箱格式
            if (!validateEmail(email)) {
                showToast('<i class="fas fa-exclamation-triangle" style="color: var(--danger-color)"></i> 请输入正确购买的账号哦！输入的是你购买登录的账号，不是网址请不要搞混了哦~');
                return;
            }

            try {
                // 使用更简单的加载提示
                resultsDiv.innerHTML = '<div class="loading"><div class="loading-spinner"></div><p>正在查询中...</p></div>';
                
                const apiUrl = `https://apimail.552500.xyz/admin/mails?limit=20&offset=0&email=${encodeURIComponent(email)}`;
                
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'x-admin-auth': 'hao123456'
                    },
                    cache: 'no-store'
                });

                if (!response.ok) {
                    throw new Error(`API 请求失败: ${response.status}`);
                }

                const data = await response.json();

                if (!data.results?.length) {
                    resultsDiv.innerHTML = '<div class="no-results"><i class="fas fa-inbox"></i><p>未找到相关邮件，请检查是否点击发送验证码 或 登录与查询账号是否保持一致</p></div>';
                    return;
                }

                // 优化邮件筛选逻辑
                const latestEmail = data.results.find(email => 
                    email.address.toLowerCase() === emailInput.value.toLowerCase()
                );

                if (!latestEmail) {
                    resultsDiv.innerHTML = '<div class="no-results"><i class="fas fa-inbox"></i><p>未找到相关邮件，请检查是否点击发送验证码 或 登录与查询账号是否保持一致</p></div>';
                    return;
                }

                // 提前准备数据
                const emailData = EmailDecoder.extractContent(latestEmail.raw);
                const formattedTime = formatTime(latestEmail.created_at);

                // 使用模板字符串一次性构建 HTML
                resultsDiv.innerHTML = `
                    <div class="email-item">
                        ${emailData.verificationCode ? `
                            <div class="timestamp-container">
                                <div class="validity">
                                    <i class="fas fa-clock"></i>
                                    <span>验证码有效时间（10分钟）</span>
                                </div>
                                <p class="timestamp">时间：<span>${formattedTime}</span></p>
                            </div>
                            <div class="verification-container">
                                <p class="verification-code">${emailData.verificationCode}</p>
                                <button class="copy-button" onclick="copyCode('${emailData.verificationCode}')">
                                    <i class="fas fa-copy"></i> 点击复制
                                </button>
                            </div>
                        ` : '<div class="no-results"><i class="fas fa-search"></i><p>未找到验证码</p></div>'}
                    </div>
                `;

            } catch (error) {
                console.error('查询错误:', error);
                resultsDiv.innerHTML = `
                    <div class="error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>查询出错：${error.message}</p>
                        <p class="error-details">请检查网络连接或稍后重试</p>
                    </div>
                `;
            }
        }

        // 优化复制功能
        const copyCode = (() => {
            let lastCopyTime = 0;
            const cooldown = 1000; // 1秒冷却时间

            return async (code) => {
                const now = Date.now();
                if (now - lastCopyTime < cooldown) return;
                
                lastCopyTime = now;
                try {
                    await navigator.clipboard.writeText(code);
                    showToast('复制成功');  // 确保显示提示
                } catch (err) {
                    console.error('复制失败:', err);
                    showToast('复制失败，请手动复制');
                }
            };
        })();

        // 优化 Toast 显示
        const showToast = (() => {
            let currentToast = null;
            
            return (message) => {
                if (currentToast) {
                    currentToast.remove();
                }

                const toast = document.createElement('div');
                toast.className = 'toast';
                
                // 检查是否已经包含HTML标签
                if (message.includes('<i class=')) {
                    toast.innerHTML = message;
                } else {
                    toast.innerHTML = message === '复制成功' ? 
                        `<i class="fas fa-check-circle" style="color: var(--success-color)"></i> ${message}` : 
                        `<i class="fas fa-exclamation-circle" style="color: var(--danger-color)"></i> ${message}`;
                }
                
                // 将 toast 添加到 body
                document.body.appendChild(toast);
                currentToast = toast;

                // 显示 toast
                requestAnimationFrame(() => {
                    toast.classList.add('show');
                });

                // 自动隐藏
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.remove();
                        }
                        if (currentToast === toast) {
                            currentToast = null;
                        }
                    }, 300);
                }, 3000); // 延长显示时间，让用户有足够时间阅读提示
            };
        })();

        // 添加键盘事件监听，按回车键触发搜索
        document.getElementById('emailInput').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                searchEmails();
            }
        });

        // 添加页面加载完成后自动聚焦到输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('emailInput').focus();
        });

        // 添加网络状态监听
        window.addEventListener('online', function() {
            if (document.querySelector('.error')) {
                showToast('网络已恢复，可以重新查询');
            }
        });

        window.addEventListener('offline', function() {
            showToast('网络连接已断开，请检查网络设置');
        });

        // 添加响应式调整
        function adjustUIForScreenSize() {
            const isMobile = window.innerWidth <= 768;
            const verificationCode = document.querySelector('.verification-code');
            
            if (verificationCode) {
                if (isMobile && verificationCode.textContent.length > 6) {
                    verificationCode.style.fontSize = '32px';
                    verificationCode.style.letterSpacing = '2px';
                } else {
                    verificationCode.style.fontSize = '';
                    verificationCode.style.letterSpacing = '';
                }
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', debounce(adjustUIForScreenSize, 250));
        
        // 页面加载后调整UI
        document.addEventListener('DOMContentLoaded', adjustUIForScreenSize);
    </script>
</body>
</html> 