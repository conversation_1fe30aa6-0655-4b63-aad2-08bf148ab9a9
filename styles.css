.container {
    max-width: 100%;
    padding: 15px;
    margin: 0 auto;
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
    padding: 12px;
    font-size: 16px; /* 移动端更适合的字体大小 */
    border: 1px solid #ddd;
    border-radius: 8px;
}

.search-button {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

.email-item {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
}

.verification-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.verification-code {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
    word-break: break-all; /* 确保长验证码可以换行 */
}

.copy-button {
    width: 100%;
    max-width: 200px;
    padding: 10px;
    font-size: 16px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

.timestamp {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.no-results {
    text-align: center;
    color: #666;
    font-size: 16px;
    padding: 20px;
}

/* 添加响应式设计 */
@media screen and (min-width: 768px) {
    .container {
        max-width: 600px;
    }
    
    .search-container {
        flex-direction: row;
    }
    
    .search-input {
        flex: 1;
    }
    
    .search-button {
        width: auto;
        min-width: 120px;
    }
} 